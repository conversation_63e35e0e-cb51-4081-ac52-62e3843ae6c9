package com.learning.designpatterns;

import com.learning.designpatterns.creational.singleton.SingletonDemo;
import com.learning.designpatterns.creational.factory.FactoryMethodDemo;
import com.learning.designpatterns.creational.builder.BuilderDemo;
import com.learning.designpatterns.creational.abstractfactory.AbstractFactoryDemo;
import com.learning.designpatterns.creational.prototype.PrototypeDemo;
import com.learning.designpatterns.behavioral.observer.ObserverDemo;
import com.learning.designpatterns.behavioral.strategy.StrategyDemo;
import com.learning.designpatterns.behavioral.command.CommandDemo;
import com.learning.designpatterns.behavioral.chainofresponsibility.ChainOfResponsibilityDemo;
import com.learning.designpatterns.behavioral.state.StateDemo;
import com.learning.designpatterns.behavioral.templatemethod.TemplateMethodDemo;
import com.learning.designpatterns.behavioral.visitor.VisitorDemo;
import com.learning.designpatterns.behavioral.mediator.MediatorDemo;
import com.learning.designpatterns.structural.adapter.AdapterDemo;
import com.learning.designpatterns.structural.decorator.DecoratorDemo;
import com.learning.designpatterns.structural.facade.FacadeDemo;
import com.learning.designpatterns.structural.proxy.ProxyDemo;
import com.learning.designpatterns.structural.composite.CompositeDemo;
import com.learning.designpatterns.structural.bridge.BridgeDemo;
import com.learning.designpatterns.structural.flyweight.FlyweightDemo;
import lombok.extern.slf4j.Slf4j;

import java.util.Scanner;

/**
 * 设计模式学习应用主类
 * 
 * 这是整个设计模式学习项目的入口点，提供了一个交互式菜单
 * 让用户可以选择运行不同的设计模式演示
 * 
 * <AUTHOR>
 */
@Slf4j
public class DesignPatternsApplication {
    
    private static final Scanner scanner = new Scanner(System.in);
    
    public static void main(String[] args) {
        log.info("🎯 欢迎使用Java设计模式学习系统！");
        log.info("📚 本系统包含23种经典设计模式的完整实现和演示");
        
        showWelcomeMessage();
        
        while (true) {
            showMainMenu();
            int choice = getUserChoice();
            
            if (choice == 0) {
                log.info("👋 感谢使用设计模式学习系统，再见！");
                break;
            }
            
            executeChoice(choice);
            
            System.out.println("\n按回车键继续...");
            scanner.nextLine();
        }
        
        scanner.close();
    }
    
    /**
     * 显示欢迎信息
     */
    private static void showWelcomeMessage() {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("🏗️  Java 设计模式学习系统");
        System.out.println("=".repeat(60));
        System.out.println("📖 学习目标：");
        System.out.println("   • 理解23种经典设计模式的原理和应用");
        System.out.println("   • 掌握面向对象设计的最佳实践");
        System.out.println("   • 提高代码的可维护性和可扩展性");
        System.out.println("=".repeat(60));
    }
    
    /**
     * 显示主菜单
     */
    private static void showMainMenu() {
        System.out.println("\n📋 请选择要学习的设计模式类别：");
        System.out.println();
        System.out.println("🏗️  创建型模式 (Creational Patterns)");
        System.out.println("   1. 单例模式 (Singleton Pattern)");
        System.out.println("   2. 工厂方法模式 (Factory Method Pattern)");
        System.out.println("   3. 建造者模式 (Builder Pattern)");
        System.out.println("   4. 抽象工厂模式 (Abstract Factory Pattern)");
        System.out.println("   5. 原型模式 (Prototype Pattern)");
        System.out.println();
        System.out.println("🔧 结构型模式 (Structural Patterns)");
        System.out.println("   6. 适配器模式 (Adapter Pattern)");
        System.out.println("   7. 装饰器模式 (Decorator Pattern)");
        System.out.println("   8. 外观模式 (Facade Pattern)");
        System.out.println("   9. 代理模式 (Proxy Pattern)");
        System.out.println("   10. 组合模式 (Composite Pattern)");
        System.out.println("   11. 桥接模式 (Bridge Pattern)");
        System.out.println("   12. 享元模式 (Flyweight Pattern)");
        System.out.println();
        System.out.println("🎭 行为型模式 (Behavioral Patterns)");
        System.out.println("   13. 观察者模式 (Observer Pattern)");
        System.out.println("   14. 策略模式 (Strategy Pattern)");
        System.out.println("   15. 命令模式 (Command Pattern)");
        System.out.println("   16. 责任链模式 (Chain of Responsibility Pattern)");
        System.out.println("   17. 状态模式 (State Pattern)");
        System.out.println("   18. 模板方法模式 (Template Method Pattern)");
        System.out.println("   19. 访问者模式 (Visitor Pattern)");
        System.out.println("   20. 中介者模式 (Mediator Pattern)");
        System.out.println();
        System.out.println("🧪 其他选项");
        System.out.println("   21. 运行所有演示");
        System.out.println("   22. 查看项目信息");
        System.out.println("   0. 退出系统");
        System.out.println();
        System.out.print("请输入您的选择 (0-22): ");
    }
    
    /**
     * 获取用户选择
     */
    private static int getUserChoice() {
        try {
            String input = scanner.nextLine().trim();
            return Integer.parseInt(input);
        } catch (NumberFormatException e) {
            log.warn("输入无效，请输入数字");
            return -1;
        }
    }
    
    /**
     * 执行用户选择
     */
    private static void executeChoice(int choice) {
        try {
            switch (choice) {
                case 1 -> {
                    log.info("🔄 启动单例模式演示...");
                    SingletonDemo.main(new String[]{});
                }
                case 2 -> {
                    log.info("🔄 启动工厂方法模式演示...");
                    FactoryMethodDemo.main(new String[]{});
                }
                case 3 -> {
                    log.info("🔄 启动建造者模式演示...");
                    BuilderDemo.main(new String[]{});
                }
                case 4 -> {
                    log.info("🔄 启动抽象工厂模式演示...");
                    AbstractFactoryDemo.main(new String[]{});
                }
                case 5 -> {
                    log.info("🔄 启动原型模式演示...");
                    PrototypeDemo.main(new String[]{});
                }
                case 6 -> {
                    log.info("🔄 启动适配器模式演示...");
                    AdapterDemo.main(new String[]{});
                }
                case 7 -> {
                    log.info("🔄 启动装饰器模式演示...");
                    DecoratorDemo.main(new String[]{});
                }
                case 8 -> {
                    log.info("🔄 启动外观模式演示...");
                    FacadeDemo.main(new String[]{});
                }
                case 9 -> {
                    log.info("🔄 启动代理模式演示...");
                    ProxyDemo.main(new String[]{});
                }
                case 10 -> {
                    log.info("🔄 启动组合模式演示...");
                    CompositeDemo.main(new String[]{});
                }
                case 11 -> {
                    log.info("🔄 启动桥接模式演示...");
                    BridgeDemo.main(new String[]{});
                }
                case 12 -> {
                    log.info("🔄 启动享元模式演示...");
                    FlyweightDemo.main(new String[]{});
                }
                case 13 -> {
                    log.info("🔄 启动观察者模式演示...");
                    ObserverDemo.main(new String[]{});
                }
                case 14 -> {
                    log.info("🔄 启动策略模式演示...");
                    StrategyDemo.main(new String[]{});
                }
                case 15 -> {
                    log.info("🔄 启动命令模式演示...");
                    CommandDemo.main(new String[]{});
                }
                case 16 -> {
                    log.info("🔄 启动责任链模式演示...");
                    ChainOfResponsibilityDemo.main(new String[]{});
                }
                case 17 -> {
                    log.info("🔄 启动状态模式演示...");
                    StateDemo.main(new String[]{});
                }
                case 18 -> {
                    log.info("🔄 启动模板方法模式演示...");
                    TemplateMethodDemo.main(new String[]{});
                }
                case 19 -> {
                    log.info("🔄 启动访问者模式演示...");
                    VisitorDemo.main(new String[]{});
                }
                case 20 -> {
                    log.info("🔄 启动中介者模式演示...");
                    MediatorDemo.main(new String[]{});
                }
                case 21 -> {
                    log.info("🔄 运行所有可用的演示...");
                    runAllDemos();
                }
                case 22 -> {
                    showProjectInfo();
                }
                default -> {
                    log.warn("⚠️ 无效的选择，请输入 0-22 之间的数字");
                }
            }
        } catch (Exception e) {
            log.error("执行演示时发生错误", e);
        }
    }
    
    /**
     * 运行所有演示
     */
    private static void runAllDemos() {
        log.info("🚀 开始运行所有设计模式演示...");
        
        try {
            SingletonDemo.main(new String[]{});
            Thread.sleep(2000);

            FactoryMethodDemo.main(new String[]{});
            Thread.sleep(2000);

            BuilderDemo.main(new String[]{});
            Thread.sleep(2000);

            AbstractFactoryDemo.main(new String[]{});
            Thread.sleep(2000);

            PrototypeDemo.main(new String[]{});
            Thread.sleep(2000);

            AdapterDemo.main(new String[]{});
            Thread.sleep(2000);

            DecoratorDemo.main(new String[]{});
            Thread.sleep(2000);

            FacadeDemo.main(new String[]{});
            Thread.sleep(2000);

            ProxyDemo.main(new String[]{});
            Thread.sleep(2000);

            CompositeDemo.main(new String[]{});
            Thread.sleep(2000);

            BridgeDemo.main(new String[]{});
            Thread.sleep(2000);

            FlyweightDemo.main(new String[]{});
            Thread.sleep(2000);

            ObserverDemo.main(new String[]{});
            Thread.sleep(2000);

            StrategyDemo.main(new String[]{});
            Thread.sleep(2000);

            CommandDemo.main(new String[]{});
            Thread.sleep(2000);

            ChainOfResponsibilityDemo.main(new String[]{});
            Thread.sleep(2000);

            StateDemo.main(new String[]{});
            Thread.sleep(2000);

            TemplateMethodDemo.main(new String[]{});
            Thread.sleep(2000);

            VisitorDemo.main(new String[]{});
            Thread.sleep(2000);

            MediatorDemo.main(new String[]{});

            log.info("✅ 所有演示运行完成！");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("演示过程被中断", e);
        }
    }
    
    /**
     * 显示项目信息
     */
    private static void showProjectInfo() {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("📊 项目信息");
        System.out.println("=".repeat(60));
        System.out.println("项目名称: Java设计模式学习项目");
        System.out.println("版本: 1.0.0");
        System.out.println("作者: Learning Team");
        System.out.println("Java版本: 17");
        System.out.println("构建工具: Maven");
        System.out.println();
        System.out.println("📈 当前进度:");
        System.out.println("✅ 创建型模式: 5/5 (100%)");
        System.out.println("✅ 结构型模式: 7/7 (100%)");
        System.out.println("✅ 行为型模式: 8/11 (73%)");
        System.out.println("📊 总体进度: 20/23 (87%)");
        System.out.println();
        System.out.println("🎯 学习建议:");
        System.out.println("1. 先理解模式的意图和结构");
        System.out.println("2. 运行演示代码观察输出");
        System.out.println("3. 阅读源码理解实现细节");
        System.out.println("4. 尝试修改代码加深理解");
        System.out.println("5. 思考在实际项目中的应用场景");
        System.out.println("=".repeat(60));
    }
}
